// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Theia.Infrastructure.Persistence;

#nullable disable

namespace Theia.Infrastructure.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20251028143728_THEIA-1134_DeletionAuditLog")]
    partial class THEIA1134_DeletionAuditLog
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("dbo")
                .HasAnnotation("ProductVersion", "8.0.14")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Theia.Domain.Entities.ApplicationForms.ApplicationFormVersion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ApplicationFormId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompletedHtml")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("FirstPageIsStarted")
                        .HasColumnType("bit");

                    b.Property<bool>("InUse")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsComplete")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("ShowProgressBar")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StartSurveyText")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SurveyJson")
                        .HasColumnType("nvarchar(MAX)");

                    b.Property<string>("Version")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationFormId");

                    b.ToTable("ApplicationFormVersions", "dbo", t =>
                        {
                            t.HasTrigger("trg_ApplicationFormVersions_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ApplicationFormVersionsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.ApplicationForms.ApplicationForms", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApplicationFormCode")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ApplicationFormType")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationFormCode")
                        .IsUnique()
                        .HasFilter("[ApplicationFormCode] IS NOT NULL");

                    b.ToTable("ApplicationForms", "dbo", t =>
                        {
                            t.HasTrigger("trg_ApplicationForms_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ApplicationFormsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.ApplicationForms.Choice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("QuestionId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("QuestionId");

                    b.ToTable("Choices", "dbo", t =>
                        {
                            t.HasTrigger("trg_Choices_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ChoicesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.ApplicationForms.Question", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ControlFrameworkCategoryClauseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CorrectAnswer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Html")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("Reference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ShowOtherItem")
                        .HasColumnType("bit");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VisibleIf")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Weighting")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Question", "dbo", t =>
                        {
                            t.HasTrigger("trg_Question_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("QuestionHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.BrokingHouses.BrokingHouse", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("ContactName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(320)
                        .HasColumnType("nvarchar(320)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.HasKey("Id");

                    b.ToTable("BrokingHouses", "dbo", t =>
                        {
                            t.HasTrigger("trg_BrokingHouses_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("BrokingHousesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.BrokingHouses.BrokingHouseSubmission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BrokingHouseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("IsPrimaryBroker")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("SubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("BrokingHouseId");

                    b.HasIndex("SubmissionId");

                    b.ToTable("BrokingHouseSubmissions", "dbo", t =>
                        {
                            t.HasTrigger("trg_BrokingHouseSubmissions_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("BrokingHouseSubmissionsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.ControlFrameworks.ControlFramework", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("ControlFrameworks", "dbo", t =>
                        {
                            t.HasTrigger("trg_ControlFrameworks_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ControlFrameworksHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.ControlFrameworks.ControlFrameworkCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ControlFrameworkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("Reference")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<int>("Weighting")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ControlFrameworkId");

                    b.ToTable("ControlFrameworkCategories", "dbo", t =>
                        {
                            t.HasTrigger("trg_ControlFrameworkCategories_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ControlFrameworkCategoriesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.ControlFrameworks.ControlFrameworkCategoryClause", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ControlFrameworkCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("Reference")
                        .HasMaxLength(12)
                        .HasColumnType("nvarchar(12)");

                    b.Property<decimal>("Weighting")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("ControlFrameworkCategoryId");

                    b.ToTable("ControlFrameworkCategoryClauses", "dbo", t =>
                        {
                            t.HasTrigger("trg_ControlFrameworkCategoryClauses_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ControlFrameworkCategoryClausesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.DeletionAuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("CurrentTableName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeletedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeletedBySystemUser")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("DeletedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("EntityId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("TableObjectId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("DeletionAuditLogs", "dbo", t =>
                        {
                            t.HasTrigger("trg_DeletionAuditLogs_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("DeletionAuditLogsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationPermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("HostVisibility")
                        .HasColumnType("bit");

                    b.Property<bool>("IsUserDefined")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<bool>("TenantVisibility")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("AspNetPermissions", "dbo", t =>
                        {
                            t.HasTrigger("trg_AspNetPermissions_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("AspNetPermissionsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AuthId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<bool>("IsStatic")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.HasIndex("AuthId")
                        .IsUnique();

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Roles", "dbo", t =>
                        {
                            t.HasTrigger("trg_Roles_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("RolesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AvatarUri")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsStatic")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSuspended")
                        .HasColumnType("bit");

                    b.Property<string>("JobTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Surname")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("SupplierId");

                    b.ToTable("Users", "dbo", t =>
                        {
                            t.HasTrigger("trg_Users_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("UsersHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationUserAttachment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileUri")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserAttachments", "dbo", t =>
                        {
                            t.HasTrigger("trg_AspNetUserAttachments_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("AspNetUserAttachmentsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationUserClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("IsExcluded")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("ApplicationUserClaim", "dbo", t =>
                        {
                            t.HasTrigger("trg_ApplicationUserClaim_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ApplicationUserClaimHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.UserAccessibleTenant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApprovedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ApprovedByIdApplicationUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("IsCurrent")
                        .HasColumnType("bit");

                    b.Property<string>("LinkedToUserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ApprovedByIdApplicationUserId");

                    b.HasIndex("LinkedToUserId");

                    b.HasIndex("TenantId");

                    b.ToTable("UserAccessibleTenants", "dbo", t =>
                        {
                            t.HasTrigger("trg_UserAccessibleTenants_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("UserAccessibleTenantsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.UserRole", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UserRole", "dbo", t =>
                        {
                            t.HasTrigger("trg_UserRole_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("UserRoleHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Endorsement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<Guid>("IndicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.HasIndex("IndicationId");

                    b.ToTable("Endorsements", "dbo", t =>
                        {
                            t.HasTrigger("trg_Endorsements_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("EndorsementsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.HeadOfCover", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("HasWaitingPeriod")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("PolicyFormId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Section")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PolicyFormId", "Name")
                        .IsUnique();

                    b.ToTable("HeadOfCover", "dbo", t =>
                        {
                            t.HasTrigger("trg_HeadOfCover_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("HeadOfCoverHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Indication", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<string>("CreatedForId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("IncidentClaimNotifications")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IncidentResponsePanel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InclusiveNoteForIndication")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("IndicatedDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<int?>("IndicationDays")
                        .HasColumnType("int");

                    b.Property<Guid>("IndicationRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("InsurerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LawAndJurisdiction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<DateTimeOffset?>("PolicyEndDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("PolicyStartDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid?>("PolicyWordingId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("RetroactiveDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("RetroactiveDateType")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StatusChangeReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StatusChangedById")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedForId");

                    b.HasIndex("IndicationRequestId");

                    b.HasIndex("InsurerId");

                    b.HasIndex("PolicyWordingId");

                    b.HasIndex("StatusChangedById");

                    b.ToTable("Indications", "dbo", t =>
                        {
                            t.HasTrigger("trg_Indications_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("IndicationsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Layer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Excess")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsSharedWithOrg")
                        .HasColumnType("bit");

                    b.Property<decimal>("Limit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("SubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("TargetPremium")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("SubmissionId");

                    b.ToTable("Layers", "dbo", t =>
                        {
                            t.HasTrigger("trg_Layers_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("LayersHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Option", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("BrokeragePercentage")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<Guid>("IndicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("InsurerLine")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsSelected")
                        .HasColumnType("bit");

                    b.Property<int>("LiabilityAgreement")
                        .HasColumnType("int");

                    b.Property<decimal>("LimitOfLiability")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<decimal>("Premium")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("IndicationId");

                    b.ToTable("Options", "dbo", t =>
                        {
                            t.HasTrigger("trg_Options_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OptionsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.OptionHeadsOfCovers", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<Guid>("HeadsOfCoverId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsCovered")
                        .HasColumnType("bit");

                    b.Property<decimal>("LiabilityAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OptionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("RetentionAmount")
                        .HasColumnType("int");

                    b.Property<int?>("WaitingPeriod")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("HeadsOfCoverId");

                    b.HasIndex("OptionId");

                    b.ToTable("OptionHeadsOfCovers", "dbo", t =>
                        {
                            t.HasTrigger("trg_OptionHeadsOfCovers_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OptionHeadsOfCoversHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.PolicyForm", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<byte[]>("FileContent")
                        .IsRequired()
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("FileContentType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("PolicyForms", "dbo", t =>
                        {
                            t.HasTrigger("trg_PolicyForms_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("PolicyFormsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.QuotaShare", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<Guid>("LeadOptionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.HasIndex("LeadOptionId")
                        .IsUnique();

                    b.ToTable("QuotaShares", "dbo", t =>
                        {
                            t.HasTrigger("trg_QuotaShares_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("QuotaSharesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.QuotaShareFollower", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("DeclineReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("FinalLineSize")
                        .HasColumnType("decimal(18,2)");

                    b.Property<Guid>("InsurerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("MaximumLineSize")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("QuotaShareId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Subjectivities")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("InsurerId");

                    b.HasIndex("QuotaShareId");

                    b.ToTable("QuotaShareFollowers", "dbo", t =>
                        {
                            t.HasTrigger("trg_QuotaShareFollowers_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("QuotaShareFollowersHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.QuotaShareFollowerUnderwriter", b =>
                {
                    b.Property<Guid>("QuotaShareFollowerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UnderwriterId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("QuotaShareFollowerId", "UnderwriterId");

                    b.HasIndex("UnderwriterId");

                    b.ToTable("QuotaShareFollowerUnderwriter", "dbo", t =>
                        {
                            t.HasTrigger("trg_QuotaShareFollowerUnderwriter_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("QuotaShareFollowerUnderwriterHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Subjectivity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<Guid>("IndicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<bool>("Satisfied")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("IndicationId");

                    b.ToTable("Subjectivities", "dbo", t =>
                        {
                            t.HasTrigger("trg_Subjectivities_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubjectivitiesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Insurer", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("ContactName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(320)
                        .HasColumnType("nvarchar(320)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.HasKey("Id");

                    b.ToTable("Insurers", "dbo", t =>
                        {
                            t.HasTrigger("trg_Insurers_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("InsurersHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.LossType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.ToTable("LossTypes", "dbo", t =>
                        {
                            t.HasTrigger("trg_LossTypes_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("LossTypesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Notifications.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AdditionalData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<int>("EventType")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("SenderId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("SentToTenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("Notifications", "dbo", t =>
                        {
                            t.HasTrigger("trg_Notifications_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("NotificationsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Notifications.NotificationRecipient", b =>
                {
                    b.Property<Guid?>("NotificationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RecipientId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("HasBeenRead")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("NotificationId", "RecipientId");

                    b.ToTable("NotificationRecipients", "dbo", t =>
                        {
                            t.HasTrigger("trg_NotificationRecipients_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("NotificationRecipientsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.OrgBrokingAssociation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BrokingId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrgId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.HasIndex("BrokingId");

                    b.HasIndex("OrgId");

                    b.ToTable("OrgBrokingAssociations", "dbo", t =>
                        {
                            t.HasTrigger("trg_OrgBrokingAssociations_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OrgBrokingAssociationsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.OrganisationRequests.OrganisationRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AdminEmail")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("AdminFullName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Comments")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasMaxLength(320)
                        .HasColumnType("nvarchar(320)");

                    b.Property<string>("ContactName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ContactPhoneNumber")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("OrganisationRequests", "dbo", t =>
                        {
                            t.HasTrigger("trg_OrganisationRequests_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OrganisationRequestsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.OrganisationRequests.OrganisationRequestBroker", b =>
                {
                    b.Property<Guid>("OrganisationRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BrokerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("OrganisationRequestId", "BrokerId");

                    b.HasIndex("BrokerId");

                    b.ToTable("OrganisationRequestBrokers", "dbo", t =>
                        {
                            t.HasTrigger("trg_OrganisationRequestBrokers_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OrganisationRequestBrokersHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Organisation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("ApproximateContractsNumber")
                        .HasColumnType("int");

                    b.Property<string>("ApproximateNumberOfPersonalRecordsStoredInSingleDb")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("AverageContractDuration")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("AverageContractSize")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasMaxLength(320)
                        .HasColumnType("nvarchar(320)");

                    b.Property<string>("ContactName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ContactPhoneNumber")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<int?>("EmployeesCount")
                        .HasColumnType("int");

                    b.Property<string>("HqAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HqCity")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("HqCountryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("HqPostcode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("IndustryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("NumberOfBiometricRecords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("NumberOfEndpoints")
                        .HasColumnType("bigint");

                    b.Property<string>("NumberOfPaymentCardInformationRecords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NumberOfPersonalHealthInformationRecords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NumberOfPersonalIdentifiableInformationRecords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("NumberOfServers")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("TotalHardwareItValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalHardwareOtValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Website")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("YearEstablished")
                        .HasColumnType("int");

                    b.ComplexProperty<Dictionary<string, object>>("Currency", "Theia.Domain.Entities.Organisations.Organisation.Currency#Currency", b1 =>
                        {
                            b1.Property<string>("CurrencySymbol")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("ISO3LetterCurrencySymbol")
                                .HasColumnType("nvarchar(max)");
                        });

                    b.HasKey("Id");

                    b.HasIndex("HqCountryId");

                    b.HasIndex("IndustryId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("TenantId")
                        .IsUnique()
                        .HasFilter("[TenantId] IS NOT NULL");

                    b.ToTable("Organisations", "dbo", t =>
                        {
                            t.HasTrigger("trg_Organisations_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OrganisationsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Contract", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ClientName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("ContractValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<int>("Duration")
                        .HasColumnType("int");

                    b.Property<Guid>("IndustryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("ProductServiceName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("IndustryId");

                    b.HasIndex("OrganisationId");

                    b.ToTable("Contracts", "dbo", t =>
                        {
                            t.HasTrigger("trg_Contracts_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ContractsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.DataCentreLocation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("NumberOfDataCentres")
                        .HasColumnType("bigint");

                    b.Property<Guid>("OrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("OrganisationId");

                    b.ToTable("DataCentres", "dbo", t =>
                        {
                            t.HasTrigger("trg_DataCentres_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("DataCentresHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Finances.AnnualFinances", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("OrganisationId");

                    b.ToTable("AnnualFinances", "dbo", t =>
                        {
                            t.HasTrigger("trg_AnnualFinances_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("AnnualFinancesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Finances.CountryRevenue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<byte>("Revenue")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("OrganisationId");

                    b.ToTable("CountryRevenues", "dbo", t =>
                        {
                            t.HasTrigger("trg_CountryRevenues_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("CountryRevenuesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Finances.IndustryRevenue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<Guid>("IndustryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<byte>("Revenue")
                        .HasColumnType("tinyint");

                    b.Property<int>("YearStarted")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("IndustryId");

                    b.HasIndex("OrganisationId");

                    b.ToTable("IndustryRevenues", "dbo", t =>
                        {
                            t.HasTrigger("trg_IndustryRevenues_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("IndustryRevenuesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Shared.Country", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ExposureLevel")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("RegionId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("RegionId");

                    b.ToTable("Countries", "dbo", t =>
                        {
                            t.HasTrigger("trg_Countries_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("CountriesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Shared.Industry", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<int>("ExposureLevel")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.ToTable("Industries", "dbo", t =>
                        {
                            t.HasTrigger("trg_Industries_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("IndustriesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Shared.Region", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<int>("ExposureLevel")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.ToTable("Regions", "dbo", t =>
                        {
                            t.HasTrigger("trg_Regions_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("RegionsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<Guid>("LayerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("LimitOfLiability")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("RequestedByBrokingHouseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("ShowUnderlying")
                        .HasColumnType("bit");

                    b.Property<string>("SubmissionName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("LayerId");

                    b.HasIndex("RequestedByBrokingHouseId");

                    b.ToTable("BrokerSubmissions", "dbo", t =>
                        {
                            t.HasTrigger("trg_BrokerSubmissions_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("BrokerSubmissionsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmissionApplicationForm", b =>
                {
                    b.Property<Guid>("BrokerSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("OrganisationSubmissionApplicationFormId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("BrokerSubmissionId", "OrganisationSubmissionApplicationFormId");

                    b.HasIndex("OrganisationSubmissionApplicationFormId");

                    b.ToTable("BrokerSubmissionApplicationForms", "dbo", t =>
                        {
                            t.HasTrigger("trg_BrokerSubmissionApplicationForms_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("BrokerSubmissionApplicationFormsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmissionBroker", b =>
                {
                    b.Property<Guid>("BrokerSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("BrokerSubmissionId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("BrokerSubmissionBroker", "dbo", t =>
                        {
                            t.HasTrigger("trg_BrokerSubmissionBroker_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("BrokerSubmissionBrokerHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmissionSubmissionFile", b =>
                {
                    b.Property<Guid>("BrokerSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SubmissionFileId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("BrokerSubmissionId", "SubmissionFileId");

                    b.HasIndex("SubmissionFileId");

                    b.ToTable("BrokerSubmissionSubmissionFiles", "dbo", t =>
                        {
                            t.HasTrigger("trg_BrokerSubmissionSubmissionFiles_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("BrokerSubmissionSubmissionFilesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmissionUnderwriter", b =>
                {
                    b.Property<Guid>("BrokerSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("BrokerSubmissionId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("BrokerSubmissionUnderwriters", "dbo", t =>
                        {
                            t.HasTrigger("trg_BrokerSubmissionUnderwriters_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("BrokerSubmissionUnderwritersHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmissionView", b =>
                {
                    b.Property<Guid>("BrokerSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("BrokerSubmissionId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("BrokerSubmissionViews", "dbo", t =>
                        {
                            t.HasTrigger("trg_BrokerSubmissionViews_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("BrokerSubmissionViewsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Submission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<DateTimeOffset>("DueBy")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("HasBeenSentToOrganisation")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSubmitted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVoided")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid?>("PrimaryBrokingHouseTenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RequestedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<Guid>("RequestedForOrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("RequestedOnDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<byte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<string>("SubmissionName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SubmissionType")
                        .HasColumnType("int");

                    b.Property<string>("SubmissionVoidedBy")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset?>("SubmissionVoidedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("SubmittedBy")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset?>("SubmittedOnDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("TenantRequestedById")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("PrimaryBrokingHouseTenantId");

                    b.HasIndex("RequestedBy");

                    b.HasIndex("RequestedForOrganisationId");

                    b.HasIndex("SubmissionVoidedBy");

                    b.HasIndex("SubmittedBy");

                    b.HasIndex("TenantRequestedById");

                    b.ToTable("Submissions", "dbo", t =>
                        {
                            t.HasTrigger("trg_Submissions_Audit");
                        });

                    b.HasDiscriminator<int>("SubmissionType");

                    b
                        .UseTphMappingStrategy()
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubmissionsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionApplicationForm", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ApplicationFormVersionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CompletedOn")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("HasBeenModifiedByBroker")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PercentageComplete")
                        .HasColumnType("int");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("Signature")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Signed")
                        .HasColumnType("bit");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("SubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SurveyAnswers")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationFormVersionId");

                    b.HasIndex("SubmissionId");

                    b.ToTable("SubmissionApplicationForms", "dbo", t =>
                        {
                            t.HasTrigger("trg_SubmissionApplicationForms_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubmissionApplicationFormsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BlobName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContainerName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OriginalName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("SubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Uri")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SubmissionId");

                    b.HasIndex("TenantId");

                    b.ToTable("SubmissionFiles", "dbo", t =>
                        {
                            t.HasTrigger("trg_SubmissionFiles_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubmissionFilesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionQuestion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("AskedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("AskedById")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<Guid>("BrokerSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<DateTime?>("EditedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AskedById");

                    b.HasIndex("BrokerSubmissionId");

                    b.ToTable("SubmissionQuestions", "dbo", t =>
                        {
                            t.HasTrigger("trg_SubmissionQuestions_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubmissionQuestionsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionQuestionAnswer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("AnsweredAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("AnsweredById")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<DateTime?>("EditedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("QuestionId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AnsweredById");

                    b.HasIndex("QuestionId");

                    b.ToTable("SubmissionQuestionAnswers", "dbo", t =>
                        {
                            t.HasTrigger("trg_SubmissionQuestionAnswers_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubmissionQuestionAnswersHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionSupplier", b =>
                {
                    b.Property<Guid>("SubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid?>("SupplierApplicationFormVersionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SupplierSubmissionRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("SubmissionId", "SupplierId");

                    b.HasIndex("SupplierApplicationFormVersionId");

                    b.HasIndex("SupplierId");

                    b.HasIndex("SupplierSubmissionRequestId");

                    b.ToTable("SubmissionSupplier", "dbo", t =>
                        {
                            t.HasTrigger("trg_SubmissionSupplier_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubmissionSupplierHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionView", b =>
                {
                    b.Property<Guid>("SubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ViewedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("SubmissionId", "ViewedById");

                    b.HasIndex("ViewedById");

                    b.ToTable("SubmissionViews", "dbo", t =>
                        {
                            t.HasTrigger("trg_SubmissionViews_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubmissionViewsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Suppliers.SubmissionSupplierAppFormVersionSnapshot", b =>
                {
                    b.Property<Guid>("SnapshotId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SubmissionsSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SubmissionsSupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("TheiaAnalysisJobId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("SnapshotId", "SubmissionsSubmissionId", "SubmissionsSupplierId");

                    b.HasIndex("TheiaAnalysisJobId");

                    b.HasIndex("SubmissionsSubmissionId", "SubmissionsSupplierId");

                    b.ToTable("SubmissionSupplierAppFormVersionSnapshots", "dbo", t =>
                        {
                            t.HasTrigger("trg_SubmissionSupplierAppFormVersionSnapshots_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubmissionSupplierAppFormVersionSnapshotsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Suppliers.SubmissionSupplierProductSnapshot", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("IsSupplierProduct")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("SubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Version")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SubmissionId");

                    b.HasIndex("SupplierId");

                    b.ToTable("SubmissionSupplierProductSnapshots", "dbo", t =>
                        {
                            t.HasTrigger("trg_SubmissionSupplierProductSnapshots_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubmissionSupplierProductSnapshotsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Suppliers.SubmissionSupplierSupplierFile", b =>
                {
                    b.Property<Guid>("AssociatedFileId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SubmissionsSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SubmissionsSupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("AssociatedFileId", "SubmissionsSubmissionId", "SubmissionsSupplierId");

                    b.HasIndex("SubmissionsSubmissionId", "SubmissionsSupplierId");

                    b.ToTable("SubmissionSupplierSupplierFile", "dbo", t =>
                        {
                            t.HasTrigger("trg_SubmissionSupplierSupplierFile_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubmissionSupplierSupplierFileHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Wholesale.WholesaleSubmissionBroker", b =>
                {
                    b.Property<Guid>("WholesaleSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BrokerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("WholesaleSubmissionId", "BrokerId");

                    b.HasIndex("BrokerId");

                    b.ToTable("WholesaleSubmissionBroker", "dbo", t =>
                        {
                            t.HasTrigger("trg_WholesaleSubmissionBroker_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("WholesaleSubmissionBrokerHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationAssociatedSupplierProduct", b =>
                {
                    b.Property<Guid>("SupplierProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("OrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid?>("SubmissionSupplierSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SubmissionSupplierSupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("SupplierProductId", "OrganisationId");

                    b.HasIndex("OrganisationId");

                    b.HasIndex("SubmissionSupplierSubmissionId", "SubmissionSupplierSupplierId");

                    b.ToTable("OrganisationAssociatedSupplierProducts", "dbo", t =>
                        {
                            t.HasTrigger("trg_OrganisationAssociatedSupplierProducts_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OrganisationAssociatedSupplierProductsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSuggestedSupplierProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("SuggestedForSupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Version")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("OrganisationId");

                    b.HasIndex("SuggestedForSupplierId");

                    b.ToTable("OrganisationSuggestedSupplierProducts", "dbo", t =>
                        {
                            t.HasTrigger("trg_OrganisationSuggestedSupplierProducts_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OrganisationSuggestedSupplierProductsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSupplierAssociation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("ContractStart")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<int?>("Criticality")
                        .HasColumnType("int");

                    b.Property<bool>("Inactive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<DateTimeOffset?>("RenewalDate")
                        .HasColumnType("datetimeoffset");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SupplierMainContactEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierMainContactLandline")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierMainContactMobile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SupplierMainContactName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("OrganisationId");

                    b.HasIndex("SupplierId");

                    b.ToTable("OrganisationSupplierAssociations", "dbo", t =>
                        {
                            t.HasTrigger("trg_OrganisationSupplierAssociations_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OrganisationSupplierAssociationsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSupplierAssociationServiceType", b =>
                {
                    b.Property<Guid>("SupplierServiceTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AssociationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("SupplierServiceTypeId", "AssociationId");

                    b.HasIndex("AssociationId");

                    b.ToTable("OrganisationSupplierAssociationServiceType", "dbo", t =>
                        {
                            t.HasTrigger("trg_OrganisationSupplierAssociationServiceType_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OrganisationSupplierAssociationServiceTypeHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSupplierAssociationSupplierService", b =>
                {
                    b.Property<Guid?>("OrganisationSupplierAssociationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SupplierServiceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("OrganisationSupplierAssociationId", "SupplierServiceId");

                    b.HasIndex("SupplierServiceId");

                    b.ToTable("OrganisationSupplierAssociationSupplierServices", "dbo", t =>
                        {
                            t.HasTrigger("trg_OrganisationSupplierAssociationSupplierServices_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("OrganisationSupplierAssociationSupplierServicesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.AnalysedControlFramework", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ControlFrameworkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<Guid?>("IndicationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("IndicationRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<decimal>("Score")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ScorePosition")
                        .HasColumnType("int");

                    b.Property<Guid?>("SubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SupplierApplicationFormVersionSnapshotId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TheiaAnalysisJobId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ControlFrameworkId");

                    b.HasIndex("IndicationId");

                    b.HasIndex("IndicationRequestId");

                    b.HasIndex("SubmissionId");

                    b.HasIndex("SupplierApplicationFormVersionSnapshotId");

                    b.HasIndex("TheiaAnalysisJobId");

                    b.ToTable("AnalysedControlFrameworks", "dbo", t =>
                        {
                            t.HasTrigger("trg_AnalysedControlFrameworks_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("AnalysedControlFrameworksHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.AnalysedControlFrameworkCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AnalysedControlFrameworkId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ControlFrameworkCategoryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<decimal>("Score")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("AnalysedControlFrameworkId");

                    b.HasIndex("ControlFrameworkCategoryId");

                    b.ToTable("AnalysedControlFrameworkCategories", "dbo", t =>
                        {
                            t.HasTrigger("trg_AnalysedControlFrameworkCategories_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("AnalysedControlFrameworkCategoriesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.AnalysedControlFrameworkCategoryClause", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AnalysedControlFrameworkCategoryId")
                        .IsRequired()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ControlFrameworkCategoryClauseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("IsInformationalOnly")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("SatisfactoryState")
                        .HasColumnType("int");

                    b.Property<Guid?>("SubmissionApplicationFormId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("SupplierApplicationFormVersionSnapshotId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TheiaAnalysisJobId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("TotalScore")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("AnalysedControlFrameworkCategoryId");

                    b.HasIndex("ControlFrameworkCategoryClauseId");

                    b.HasIndex("SubmissionApplicationFormId");

                    b.HasIndex("SupplierApplicationFormVersionSnapshotId");

                    b.HasIndex("TheiaAnalysisJobId");

                    b.ToTable("AnalysedControlFrameworkCategoryClauses", "dbo", t =>
                        {
                            t.HasTrigger("trg_AnalysedControlFrameworkCategoryClauses_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("AnalysedControlFrameworkCategoryClausesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.TheiaAnalysisJob", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<DateTime>("DateMovedIntoStatus")
                        .HasColumnType("datetime2");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("IndicationRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("JobType")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid?>("SubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("IndicationRequestId");

                    b.HasIndex("SubmissionId");

                    b.ToTable("TheiaAnalysisJobs", "dbo", t =>
                        {
                            t.HasTrigger("trg_TheiaAnalysisJobs_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("TheiaAnalysisJobsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Settings.FileStorageSettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("StorageType")
                        .HasColumnType("int");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("FileStorageSettings", "dbo", t =>
                        {
                            t.HasTrigger("trg_FileStorageSettings_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("FileStorageSettingsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Settings.IdentitySettings.LockoutSettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("AllowedForNewUsers")
                        .HasColumnType("bit");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<int>("DefaultLockoutTimeSpan")
                        .HasColumnType("int");

                    b.Property<int>("MaxFailedAccessAttempts")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("LockoutSettings", "dbo", t =>
                        {
                            t.HasTrigger("trg_LockoutSettings_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("LockoutSettingsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Settings.IdentitySettings.PasswordSettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<bool>("RequireDigit")
                        .HasColumnType("bit");

                    b.Property<bool>("RequireLowercase")
                        .HasColumnType("bit");

                    b.Property<bool>("RequireNonAlphanumeric")
                        .HasColumnType("bit");

                    b.Property<bool>("RequireUppercase")
                        .HasColumnType("bit");

                    b.Property<int>("RequiredLength")
                        .HasColumnType("int");

                    b.Property<int>("RequiredUniqueChars")
                        .HasColumnType("int");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("PasswordSettings", "dbo", t =>
                        {
                            t.HasTrigger("trg_PasswordSettings_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("PasswordSettingsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Settings.TokenSettings", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<double?>("AccessTokenTimeSpan")
                        .HasColumnType("float");

                    b.Property<int>("AccessTokenUoT")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<double?>("RefreshTokenTimeSpan")
                        .HasColumnType("float");

                    b.Property<int>("RefreshTokenUoT")
                        .HasColumnType("int");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("TokenSettings", "dbo", t =>
                        {
                            t.HasTrigger("trg_TokenSettings_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("TokenSettingsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Settings.UserTenantControl", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("OrgBrokingAssociationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("OrgBrokingAssociationId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("UserTenantControls", "dbo", t =>
                        {
                            t.HasTrigger("trg_UserTenantControls_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("UserTenantControlsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersion", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ApplicationFormVersionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompletedByUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset?>("CompletedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("IsComplete")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LastModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PercentageComplete")
                        .HasColumnType("int");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<DateTimeOffset>("RequestedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SurveyAnswers")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationFormVersionId");

                    b.HasIndex("CompletedByUserId");

                    b.HasIndex("SupplierId");

                    b.ToTable("SupplierApplicationFormVersion", "dbo", t =>
                        {
                            t.HasTrigger("trg_SupplierApplicationFormVersion_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SupplierApplicationFormVersionHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersionSnapshot", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompletedByUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<DateTimeOffset>("CreatedOnUtc")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid?>("SupplierApplicationFormVersionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SurveyAnswers")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TheiaAnalysisJobId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CompletedByUserId");

                    b.HasIndex("SupplierApplicationFormVersionId");

                    b.HasIndex("TheiaAnalysisJobId")
                        .IsUnique();

                    b.ToTable("SupplierApplicationFormVersionSnapshots", "dbo", t =>
                        {
                            t.HasTrigger("trg_SupplierApplicationFormVersionSnapshots_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SupplierApplicationFormVersionSnapshotsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<DateTimeOffset?>("DeletedAtUtc")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DeletedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Version")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("DeletedById");

                    b.HasIndex("SupplierId", "Name", "Version")
                        .IsUnique();

                    b.ToTable("Products", "dbo", t =>
                        {
                            t.HasTrigger("trg_Products_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ProductsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Services.SupplierService", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("Id");

                    b.ToTable("SupplierServices", "dbo", t =>
                        {
                            t.HasTrigger("trg_SupplierServices_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SupplierServicesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Services.SupplierServiceType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SupplierServiceTypes", "dbo", t =>
                        {
                            t.HasTrigger("trg_SupplierServiceTypes_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SupplierServiceTypesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionApplicationFormVersion", b =>
                {
                    b.Property<Guid>("SupplierSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SupplierApplicationFormVersionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("SupplierSubmissionId", "SupplierApplicationFormVersionId");

                    b.HasIndex("SupplierApplicationFormVersionId");

                    b.ToTable("SupplierSubmissionApplicationFormVersion", "dbo", t =>
                        {
                            t.HasTrigger("trg_SupplierSubmissionApplicationFormVersion_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SupplierSubmissionApplicationFormVersionHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionApplicationFormVersionSnapshot", b =>
                {
                    b.Property<Guid>("SupplierSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SupplierApplicationFormVersionSnapshotId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("SupplierSubmissionId", "SupplierApplicationFormVersionSnapshotId");

                    b.HasIndex("SupplierApplicationFormVersionSnapshotId");

                    b.ToTable("SupplierSubmissionApplicationFormVersionSnapshot", "dbo", t =>
                        {
                            t.HasTrigger("trg_SupplierSubmissionApplicationFormVersionSnapshot_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SupplierSubmissionApplicationFormVersionSnapshotHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset?>("CompletedOnUtc")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<string>("CreatedByUserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<DateTimeOffset>("CreatedOnUtc")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("OrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("OrganisationId");

                    b.HasIndex("SupplierId");

                    b.ToTable("SupplierSubmissions", "dbo", t =>
                        {
                            t.HasTrigger("trg_SupplierSubmissions_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SupplierSubmissionsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequestFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AssociatedSupplierFileId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("SupplierSubmissionId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AssociatedSupplierFileId");

                    b.HasIndex("SupplierSubmissionId");

                    b.ToTable("SupplierSubmissionRequestFile", "dbo", t =>
                        {
                            t.HasTrigger("trg_SupplierSubmissionRequestFile_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SupplierSubmissionRequestFileHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Supplier", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Address")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Website")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasAlternateKey("CompanyNumber");

                    b.ToTable("Suppliers", "dbo", t =>
                        {
                            t.HasTrigger("trg_Suppliers_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SuppliersHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.SupplierFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BlobName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContainerName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("InUse")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OriginalName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<Guid>("SupplierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Uri")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Visibility")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SupplierId");

                    b.ToTable("SupplierFiles", "dbo", t =>
                        {
                            t.HasTrigger("trg_SupplierFiles_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SupplierFilesHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.SupplierRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("CreatedByOrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("ProductAndServices")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SupplierTypeId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByOrganisationId");

                    b.HasIndex("SupplierTypeId");

                    b.ToTable("SupplierRequests", "dbo", t =>
                        {
                            t.HasTrigger("trg_SupplierRequests_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SupplierRequestsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Tenants", "dbo", t =>
                        {
                            t.HasTrigger("trg_Tenants_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("TenantsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.UserTokenControl", b =>
                {
                    b.Property<Guid>("KeyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBySystemUser")
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasDefaultValueSql("SYSTEM_USER");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("SYSDATETIMEOFFSET()");

                    b.Property<bool>("HasChanges")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.HasKey("KeyId", "UserId");

                    b.ToTable("UserTokenControls", "dbo", t =>
                        {
                            t.HasTrigger("trg_UserTokenControls_Audit");
                        });

                    b
                        .ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("UserTokenControlsHistory", "history");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }))
                        .HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Organisation.OrganisationSubmission", b =>
                {
                    b.HasBaseType("Theia.Domain.Entities.Organisations.Submissions.Submission");

                    b.HasDiscriminator().HasValue(0);
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Wholesale.WholesaleSubmission", b =>
                {
                    b.HasBaseType("Theia.Domain.Entities.Organisations.Submissions.Submission");

                    b.Property<Guid>("BrokingHouseId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CancelledOrWithdrawnReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasBeenSentBackToBroker")
                        .HasColumnType("bit");

                    b.Property<Guid>("IndicationRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("IsApproved")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsWithdrawn")
                        .HasColumnType("bit");

                    b.HasIndex("BrokingHouseId");

                    b.HasIndex("IndicationRequestId");

                    b.HasDiscriminator().HasValue(1);
                });

            modelBuilder.Entity("Theia.Domain.Entities.ApplicationForms.ApplicationFormVersion", b =>
                {
                    b.HasOne("Theia.Domain.Entities.ApplicationForms.ApplicationForms", "ApplicationForm")
                        .WithMany("Versions")
                        .HasForeignKey("ApplicationFormId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApplicationForm");
                });

            modelBuilder.Entity("Theia.Domain.Entities.ApplicationForms.Choice", b =>
                {
                    b.HasOne("Theia.Domain.Entities.ApplicationForms.Question", "Question")
                        .WithMany()
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Question");
                });

            modelBuilder.Entity("Theia.Domain.Entities.BrokingHouses.BrokingHouse", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Tenant", "Tenant")
                        .WithOne("BrokingHouse")
                        .HasForeignKey("Theia.Domain.Entities.BrokingHouses.BrokingHouse", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Theia.Domain.Entities.BrokingHouses.BrokingHouseSubmission", b =>
                {
                    b.HasOne("Theia.Domain.Entities.BrokingHouses.BrokingHouse", "BrokingHouse")
                        .WithMany()
                        .HasForeignKey("BrokingHouseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.Submission", "Submission")
                        .WithMany()
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BrokingHouse");

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("Theia.Domain.Entities.ControlFrameworks.ControlFrameworkCategory", b =>
                {
                    b.HasOne("Theia.Domain.Entities.ControlFrameworks.ControlFramework", "ControlFramework")
                        .WithMany("ControlFrameworkCategories")
                        .HasForeignKey("ControlFrameworkId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("ControlFramework");
                });

            modelBuilder.Entity("Theia.Domain.Entities.ControlFrameworks.ControlFrameworkCategoryClause", b =>
                {
                    b.HasOne("Theia.Domain.Entities.ControlFrameworks.ControlFrameworkCategory", "ControlFrameworkCategory")
                        .WithMany("ControlFrameworkCategoryClauses")
                        .HasForeignKey("ControlFrameworkCategoryId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("ControlFrameworkCategory");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationPermission", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationPermission", "Parent")
                        .WithMany("Permissions")
                        .HasForeignKey("ParentId");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationUser", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Suppliers.Supplier", "Supplier")
                        .WithMany("Users")
                        .HasForeignKey("SupplierId");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationUserAttachment", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "User")
                        .WithMany("UserAttachments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationUserClaim", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "User")
                        .WithMany("Claims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.UserAccessibleTenant", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "ApprovedByIdApplicationUser")
                        .WithMany()
                        .HasForeignKey("ApprovedByIdApplicationUserId");

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "LinkedToUserIdApplicationUser")
                        .WithMany("UserAccessibleTenants")
                        .HasForeignKey("LinkedToUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Tenant", "TenantIdTenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovedByIdApplicationUser");

                    b.Navigation("LinkedToUserIdApplicationUser");

                    b.Navigation("TenantIdTenant");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.UserRole", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationRole", "Role")
                        .WithMany("Users")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "User")
                        .WithMany("Roles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Endorsement", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Indications.Indication", "Indication")
                        .WithMany("Endorsements")
                        .HasForeignKey("IndicationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Indication");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.HeadOfCover", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Indications.PolicyForm", "PolicyForm")
                        .WithMany("HeadsOfCover")
                        .HasForeignKey("PolicyFormId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PolicyForm");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Indication", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "CreatedFor")
                        .WithMany("Indications")
                        .HasForeignKey("CreatedForId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", "IndicationRequest")
                        .WithMany("Indications")
                        .HasForeignKey("IndicationRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Insurer", "Insurer")
                        .WithMany("Indications")
                        .HasForeignKey("InsurerId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Indications.PolicyForm", "PolicyWording")
                        .WithMany("Indications")
                        .HasForeignKey("PolicyWordingId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "StatusChangedBy")
                        .WithMany()
                        .HasForeignKey("StatusChangedById");

                    b.Navigation("CreatedFor");

                    b.Navigation("IndicationRequest");

                    b.Navigation("Insurer");

                    b.Navigation("PolicyWording");

                    b.Navigation("StatusChangedBy");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Layer", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.Submission", "Submission")
                        .WithMany("Layers")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Option", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Indications.Indication", "Indication")
                        .WithMany("IndicationOptions")
                        .HasForeignKey("IndicationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Indication");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.OptionHeadsOfCovers", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Indications.HeadOfCover", "HeadsOfCover")
                        .WithMany("Options")
                        .HasForeignKey("HeadsOfCoverId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Indications.Option", "Option")
                        .WithMany("HeadsOfCovers")
                        .HasForeignKey("OptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HeadsOfCover");

                    b.Navigation("Option");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.QuotaShare", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Indications.Option", "LeadOption")
                        .WithOne("QuotaShare")
                        .HasForeignKey("Theia.Domain.Entities.Indications.QuotaShare", "LeadOptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LeadOption");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.QuotaShareFollower", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Insurer", "Insurer")
                        .WithMany("QuotaShares")
                        .HasForeignKey("InsurerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Indications.QuotaShare", "QuotaShare")
                        .WithMany("Followers")
                        .HasForeignKey("QuotaShareId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Insurer");

                    b.Navigation("QuotaShare");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.QuotaShareFollowerUnderwriter", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Indications.QuotaShareFollower", "QuotaShareFollower")
                        .WithMany("Underwriters")
                        .HasForeignKey("QuotaShareFollowerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "Underwriter")
                        .WithMany("QuotaShareFollowers")
                        .HasForeignKey("UnderwriterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("QuotaShareFollower");

                    b.Navigation("Underwriter");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Subjectivity", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Indications.Indication", "Indication")
                        .WithMany("Subjectives")
                        .HasForeignKey("IndicationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Indication");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Insurer", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Tenant", "Tenant")
                        .WithOne("Insurer")
                        .HasForeignKey("Theia.Domain.Entities.Insurer", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Notifications.NotificationRecipient", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Notifications.Notification", "Notification")
                        .WithMany("NotificationRecipients")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Notification");
                });

            modelBuilder.Entity("Theia.Domain.Entities.OrgBrokingAssociation", b =>
                {
                    b.HasOne("Theia.Domain.Entities.BrokingHouses.BrokingHouse", "BrokingHouse")
                        .WithMany("OrgAssociations")
                        .HasForeignKey("BrokingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "Organisation")
                        .WithMany("BrokingAssociations")
                        .HasForeignKey("OrgId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BrokingHouse");

                    b.Navigation("Organisation");
                });

            modelBuilder.Entity("Theia.Domain.Entities.OrganisationRequests.OrganisationRequest", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "UpdatedByApplicationUser")
                        .WithMany("OrganisationRequests")
                        .HasForeignKey("UpdatedBy");

                    b.Navigation("Tenant");

                    b.Navigation("UpdatedByApplicationUser");
                });

            modelBuilder.Entity("Theia.Domain.Entities.OrganisationRequests.OrganisationRequestBroker", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "Broker")
                        .WithMany("BrokerOrganisationRequests")
                        .HasForeignKey("BrokerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.OrganisationRequests.OrganisationRequest", "OrganisationRequest")
                        .WithMany("Brokers")
                        .HasForeignKey("OrganisationRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Broker");

                    b.Navigation("OrganisationRequest");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Organisation", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Profile.Shared.Country", "HqCountry")
                        .WithMany("Organisations")
                        .HasForeignKey("HqCountryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Theia.Domain.Entities.Organisations.Profile.Shared.Industry", "Industry")
                        .WithMany("Organisations")
                        .HasForeignKey("IndustryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Theia.Domain.Entities.Tenant", "Tenant")
                        .WithOne("Organisation")
                        .HasForeignKey("Theia.Domain.Entities.Organisations.Organisation", "TenantId");

                    b.Navigation("HqCountry");

                    b.Navigation("Industry");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Contract", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Profile.Shared.Industry", "Industry")
                        .WithMany()
                        .HasForeignKey("IndustryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "Organisation")
                        .WithMany("Contracts")
                        .HasForeignKey("OrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Industry");

                    b.Navigation("Organisation");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.DataCentreLocation", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Profile.Shared.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "Organisation")
                        .WithMany("DataCentreLocations")
                        .HasForeignKey("OrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("Organisation");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Finances.AnnualFinances", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "Organisation")
                        .WithMany("AnnualFinances")
                        .HasForeignKey("OrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organisation");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Finances.CountryRevenue", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Profile.Shared.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "Organisation")
                        .WithMany("CountryRevenues")
                        .HasForeignKey("OrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("Organisation");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Finances.IndustryRevenue", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Profile.Shared.Industry", "Industry")
                        .WithMany()
                        .HasForeignKey("IndustryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "Organisation")
                        .WithMany("IndustryRevenues")
                        .HasForeignKey("OrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Industry");

                    b.Navigation("Organisation");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Shared.Country", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Profile.Shared.Region", "Region")
                        .WithMany("Countries")
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Region");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Indications.Layer", "Layer")
                        .WithMany("IndicationRequests")
                        .HasForeignKey("LayerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.BrokingHouses.BrokingHouse", "RequestedByBrokingHouse")
                        .WithMany("SubmittedBrokerSubmissions")
                        .HasForeignKey("RequestedByBrokingHouseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Layer");

                    b.Navigation("RequestedByBrokingHouse");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmissionApplicationForm", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", "BrokerSubmission")
                        .WithMany()
                        .HasForeignKey("BrokerSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.SubmissionApplicationForm", "OrganisationSubmissionApplicationForm")
                        .WithMany()
                        .HasForeignKey("OrganisationSubmissionApplicationFormId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BrokerSubmission");

                    b.Navigation("OrganisationSubmissionApplicationForm");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmissionBroker", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", "Submission")
                        .WithMany("Brokers")
                        .HasForeignKey("BrokerSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "Broker")
                        .WithMany("BrokersBrokerSubmissions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Broker");

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmissionSubmissionFile", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", "BrokerSubmission")
                        .WithMany("Files")
                        .HasForeignKey("BrokerSubmissionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.SubmissionFile", "SubmissionFile")
                        .WithMany("BrokerSubmissions")
                        .HasForeignKey("SubmissionFileId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("BrokerSubmission");

                    b.Navigation("SubmissionFile");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmissionUnderwriter", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", "BrokerSubmission")
                        .WithMany("Underwriters")
                        .HasForeignKey("BrokerSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "User")
                        .WithMany("UnderwritersBrokerSubmissions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BrokerSubmission");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmissionView", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", "BrokerSubmission")
                        .WithMany("Views")
                        .HasForeignKey("BrokerSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BrokerSubmission");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Submission", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Tenant", "PrimaryBrokingHouseTenant")
                        .WithMany()
                        .HasForeignKey("PrimaryBrokingHouseTenantId");

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "RequestedByApplicationUser")
                        .WithMany("RequestedSubmissions")
                        .HasForeignKey("RequestedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "RequestedForOrganisation")
                        .WithMany("Submissions")
                        .HasForeignKey("RequestedForOrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "SubmissionVoidedByApplicationUser")
                        .WithMany("VoidedSubmission")
                        .HasForeignKey("SubmissionVoidedBy")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "SubmittedByApplicationUser")
                        .WithMany()
                        .HasForeignKey("SubmittedBy");

                    b.HasOne("Theia.Domain.Entities.Tenant", "RequestedByTenant")
                        .WithMany("RequestedSubmissions")
                        .HasForeignKey("TenantRequestedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("PrimaryBrokingHouseTenant");

                    b.Navigation("RequestedByApplicationUser");

                    b.Navigation("RequestedByTenant");

                    b.Navigation("RequestedForOrganisation");

                    b.Navigation("SubmissionVoidedByApplicationUser");

                    b.Navigation("SubmittedByApplicationUser");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionApplicationForm", b =>
                {
                    b.HasOne("Theia.Domain.Entities.ApplicationForms.ApplicationFormVersion", "ApplicationFormVersion")
                        .WithMany()
                        .HasForeignKey("ApplicationFormVersionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.Submission", "Submission")
                        .WithMany("ApplicationForms")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("ApplicationFormVersion");

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionFile", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.Submission", "Submission")
                        .WithMany("Files")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Submission");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionQuestion", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "AskedBy")
                        .WithMany("SubmissionQuestions")
                        .HasForeignKey("AskedById")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", "BrokerSubmission")
                        .WithMany("Questions")
                        .HasForeignKey("BrokerSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AskedBy");

                    b.Navigation("BrokerSubmission");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionQuestionAnswer", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "AnsweredBy")
                        .WithMany("SubmissionQuestionAnswers")
                        .HasForeignKey("AnsweredById")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.SubmissionQuestion", "Question")
                        .WithMany("Answers")
                        .HasForeignKey("QuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AnsweredBy");

                    b.Navigation("Question");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionSupplier", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.Submission", "Submission")
                        .WithMany("Suppliers")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersion", null)
                        .WithMany("Submissions")
                        .HasForeignKey("SupplierApplicationFormVersionId");

                    b.HasOne("Theia.Domain.Entities.Suppliers.Supplier", "Supplier")
                        .WithMany("Submissions")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequest", "SupplierSubmissionRequest")
                        .WithMany()
                        .HasForeignKey("SupplierSubmissionRequestId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Submission");

                    b.Navigation("Supplier");

                    b.Navigation("SupplierSubmissionRequest");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionView", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.Submission", "Submission")
                        .WithMany("ViewedBy")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "ViewedBy")
                        .WithMany("ViewedSubmissions")
                        .HasForeignKey("ViewedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Submission");

                    b.Navigation("ViewedBy");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Suppliers.SubmissionSupplierAppFormVersionSnapshot", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersionSnapshot", "Snapshot")
                        .WithMany("Submissions")
                        .HasForeignKey("SnapshotId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Reporting.TheiaAnalysisJob", "TheiaAnalysisJob")
                        .WithMany()
                        .HasForeignKey("TheiaAnalysisJobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.SubmissionSupplier", "SubmissionSupplier")
                        .WithMany("ApplicationFormsVersionSnapshots")
                        .HasForeignKey("SubmissionsSubmissionId", "SubmissionsSupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Snapshot");

                    b.Navigation("SubmissionSupplier");

                    b.Navigation("TheiaAnalysisJob");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Suppliers.SubmissionSupplierProductSnapshot", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.Submission", "Submission")
                        .WithMany("ProductSnapshots")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Theia.Domain.Entities.Suppliers.Supplier", "Supplier")
                        .WithMany("SupplierProducts")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Submission");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Suppliers.SubmissionSupplierSupplierFile", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequestFile", "AssociatedFile")
                        .WithMany("SubmissionSupplierFiles")
                        .HasForeignKey("AssociatedFileId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.SubmissionSupplier", "SubmissionSupplier")
                        .WithMany("Files")
                        .HasForeignKey("SubmissionsSubmissionId", "SubmissionsSupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssociatedFile");

                    b.Navigation("SubmissionSupplier");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Wholesale.WholesaleSubmissionBroker", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "Broker")
                        .WithMany("WholesaleSubmissions")
                        .HasForeignKey("BrokerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.Wholesale.WholesaleSubmission", "WholesaleSubmission")
                        .WithMany("Brokers")
                        .HasForeignKey("WholesaleSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Broker");

                    b.Navigation("WholesaleSubmission");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationAssociatedSupplierProduct", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "Organisation")
                        .WithMany("AssociatedSupplierProducts")
                        .HasForeignKey("OrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.Product", "SupplierProduct")
                        .WithMany("AssociatedProducts")
                        .HasForeignKey("SupplierProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.SubmissionSupplier", null)
                        .WithMany("AssociatedProducts")
                        .HasForeignKey("SubmissionSupplierSubmissionId", "SubmissionSupplierSupplierId");

                    b.Navigation("Organisation");

                    b.Navigation("SupplierProduct");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSuggestedSupplierProduct", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "Organisation")
                        .WithMany("OrganisationSuggestedProducts")
                        .HasForeignKey("OrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.Supplier", "SuggestedForSupplier")
                        .WithMany("OrganisationSuggestedProducts")
                        .HasForeignKey("SuggestedForSupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organisation");

                    b.Navigation("SuggestedForSupplier");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSupplierAssociation", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "Organisation")
                        .WithMany("Suppliers")
                        .HasForeignKey("OrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.Supplier", "Supplier")
                        .WithMany("Organisations")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organisation");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSupplierAssociationServiceType", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSupplierAssociation", "Association")
                        .WithMany("OrganisationSupplierAssociationSupplierServiceTypes")
                        .HasForeignKey("AssociationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.Services.SupplierServiceType", "SupplierServiceType")
                        .WithMany("ServiceTypesForAssociatedSuppliers")
                        .HasForeignKey("SupplierServiceTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Association");

                    b.Navigation("SupplierServiceType");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSupplierAssociationSupplierService", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSupplierAssociation", "OrganisationSupplierAssociation")
                        .WithMany("OrganisationSupplierAssociationSupplierServices")
                        .HasForeignKey("OrganisationSupplierAssociationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.Services.SupplierService", "SupplierService")
                        .WithMany("OrganisationSupplierAssociationSupplierServices")
                        .HasForeignKey("SupplierServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("OrganisationSupplierAssociation");

                    b.Navigation("SupplierService");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.AnalysedControlFramework", b =>
                {
                    b.HasOne("Theia.Domain.Entities.ControlFrameworks.ControlFramework", "ControlFramework")
                        .WithMany("AnalysedControlFrameworks")
                        .HasForeignKey("ControlFrameworkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Indications.Indication", null)
                        .WithMany("AnalysedControlFrameworks")
                        .HasForeignKey("IndicationId");

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", "IndicationRequest")
                        .WithMany("AnalysedControlFrameworks")
                        .HasForeignKey("IndicationRequestId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.Submission", "Submission")
                        .WithMany()
                        .HasForeignKey("SubmissionId");

                    b.HasOne("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersionSnapshot", "SupplierApplicationFormVersionSnapshot")
                        .WithMany("AnalysedControlFrameworks")
                        .HasForeignKey("SupplierApplicationFormVersionSnapshotId");

                    b.HasOne("Theia.Domain.Entities.Reporting.TheiaAnalysisJob", "TheiaAnalysisJob")
                        .WithMany("AnalysedControlFrameworks")
                        .HasForeignKey("TheiaAnalysisJobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ControlFramework");

                    b.Navigation("IndicationRequest");

                    b.Navigation("Submission");

                    b.Navigation("SupplierApplicationFormVersionSnapshot");

                    b.Navigation("TheiaAnalysisJob");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.AnalysedControlFrameworkCategory", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Reporting.AnalysedControlFramework", "AnalysedControlFramework")
                        .WithMany("AnalysedControlFrameworkCategories")
                        .HasForeignKey("AnalysedControlFrameworkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.ControlFrameworks.ControlFrameworkCategory", "ControlFrameworkCategory")
                        .WithMany()
                        .HasForeignKey("ControlFrameworkCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AnalysedControlFramework");

                    b.Navigation("ControlFrameworkCategory");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.AnalysedControlFrameworkCategoryClause", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Reporting.AnalysedControlFrameworkCategory", "AnalysedControlFrameworkCategory")
                        .WithMany("AnalysedControlFrameworkCategoryClauses")
                        .HasForeignKey("AnalysedControlFrameworkCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.ControlFrameworks.ControlFrameworkCategoryClause", "ControlFrameworkCategoryClause")
                        .WithMany()
                        .HasForeignKey("ControlFrameworkCategoryClauseId");

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.SubmissionApplicationForm", "SubmissionApplicationForm")
                        .WithMany()
                        .HasForeignKey("SubmissionApplicationFormId");

                    b.HasOne("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersionSnapshot", "SupplierApplicationFormVersionSnapshot")
                        .WithMany()
                        .HasForeignKey("SupplierApplicationFormVersionSnapshotId");

                    b.HasOne("Theia.Domain.Entities.Reporting.TheiaAnalysisJob", "TheiaAnalysisJob")
                        .WithMany()
                        .HasForeignKey("TheiaAnalysisJobId");

                    b.OwnsMany("Theia.Infrastructure.Common.Models.SatisfactoryQuestionModel", "DissatisfiedByQuestions", b1 =>
                        {
                            b1.Property<Guid>("AnalysedControlFrameworkCategoryClauseId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            b1.Property<string>("Answer")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<int>("AnswerType")
                                .HasColumnType("int");

                            b1.Property<Guid?>("AssociatedToClauseId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<bool>("IsCorrect")
                                .HasColumnType("bit");

                            b1.Property<string>("QuestionComment")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("QuestionName")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("QuestionText")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<int>("QuestionWeighting")
                                .HasColumnType("int");

                            b1.HasKey("AnalysedControlFrameworkCategoryClauseId", "Id");

                            b1.ToTable("AnalysedControlFrameworkCategoryClauses", "dbo");

                            b1.ToJson("DissatisfiedByQuestions");

                            b1.WithOwner()
                                .HasForeignKey("AnalysedControlFrameworkCategoryClauseId");
                        });

                    b.OwnsMany("Theia.Infrastructure.Common.Models.SatisfactoryQuestionModel", "InformationalByQuestions", b1 =>
                        {
                            b1.Property<Guid>("AnalysedControlFrameworkCategoryClauseId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            b1.Property<string>("Answer")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<int>("AnswerType")
                                .HasColumnType("int");

                            b1.Property<Guid?>("AssociatedToClauseId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<bool>("IsCorrect")
                                .HasColumnType("bit");

                            b1.Property<string>("QuestionComment")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("QuestionName")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("QuestionText")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<int>("QuestionWeighting")
                                .HasColumnType("int");

                            b1.HasKey("AnalysedControlFrameworkCategoryClauseId", "Id");

                            b1.ToTable("AnalysedControlFrameworkCategoryClauses", "dbo");

                            b1.ToJson("InformationalByQuestions");

                            b1.WithOwner()
                                .HasForeignKey("AnalysedControlFrameworkCategoryClauseId");
                        });

                    b.OwnsMany("Theia.Infrastructure.Common.Models.SatisfactoryQuestionModel", "SatisfiedByQuestions", b1 =>
                        {
                            b1.Property<Guid>("AnalysedControlFrameworkCategoryClauseId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            b1.Property<string>("Answer")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<int>("AnswerType")
                                .HasColumnType("int");

                            b1.Property<Guid?>("AssociatedToClauseId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<bool>("IsCorrect")
                                .HasColumnType("bit");

                            b1.Property<string>("QuestionComment")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("QuestionName")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("QuestionText")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<int>("QuestionWeighting")
                                .HasColumnType("int");

                            b1.HasKey("AnalysedControlFrameworkCategoryClauseId", "Id");

                            b1.ToTable("AnalysedControlFrameworkCategoryClauses", "dbo");

                            b1.ToJson("SatisfiedByQuestions");

                            b1.WithOwner()
                                .HasForeignKey("AnalysedControlFrameworkCategoryClauseId");
                        });

                    b.OwnsMany("Theia.Infrastructure.Common.Models.SatisfactoryQuestionModel", "UnansweredByQuestions", b1 =>
                        {
                            b1.Property<Guid>("AnalysedControlFrameworkCategoryClauseId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            b1.Property<string>("Answer")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<int>("AnswerType")
                                .HasColumnType("int");

                            b1.Property<Guid?>("AssociatedToClauseId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<bool>("IsCorrect")
                                .HasColumnType("bit");

                            b1.Property<string>("QuestionComment")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("QuestionName")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("QuestionText")
                                .IsRequired()
                                .HasColumnType("nvarchar(max)");

                            b1.Property<int>("QuestionWeighting")
                                .HasColumnType("int");

                            b1.HasKey("AnalysedControlFrameworkCategoryClauseId", "Id");

                            b1.ToTable("AnalysedControlFrameworkCategoryClauses", "dbo");

                            b1.ToJson("UnansweredByQuestions");

                            b1.WithOwner()
                                .HasForeignKey("AnalysedControlFrameworkCategoryClauseId");
                        });

                    b.Navigation("AnalysedControlFrameworkCategory");

                    b.Navigation("ControlFrameworkCategoryClause");

                    b.Navigation("DissatisfiedByQuestions");

                    b.Navigation("InformationalByQuestions");

                    b.Navigation("SatisfiedByQuestions");

                    b.Navigation("SubmissionApplicationForm");

                    b.Navigation("SupplierApplicationFormVersionSnapshot");

                    b.Navigation("TheiaAnalysisJob");

                    b.Navigation("UnansweredByQuestions");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.TheiaAnalysisJob", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", "IndicationRequest")
                        .WithMany("TheiaAnalysisJobs")
                        .HasForeignKey("IndicationRequestId");

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.Submission", "Submission")
                        .WithMany("AnalysisJobs")
                        .HasForeignKey("SubmissionId");

                    b.Navigation("IndicationRequest");

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Settings.UserTenantControl", b =>
                {
                    b.HasOne("Theia.Domain.Entities.OrgBrokingAssociation", null)
                        .WithMany("Brokers")
                        .HasForeignKey("OrgBrokingAssociationId");

                    b.HasOne("Theia.Domain.Entities.Tenant", "Tenant")
                        .WithMany("UserTenantControls")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "ApplicationUser")
                        .WithMany("UserTenantControls")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApplicationUser");

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersion", b =>
                {
                    b.HasOne("Theia.Domain.Entities.ApplicationForms.ApplicationFormVersion", "ApplicationFormVersion")
                        .WithMany("Suppliers")
                        .HasForeignKey("ApplicationFormVersionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "CompletedByUser")
                        .WithMany("CompletedByUser")
                        .HasForeignKey("CompletedByUserId");

                    b.HasOne("Theia.Domain.Entities.Suppliers.Supplier", "Supplier")
                        .WithMany("ApplicationFormVersions")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApplicationFormVersion");

                    b.Navigation("CompletedByUser");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersionSnapshot", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "CompletedByUser")
                        .WithMany()
                        .HasForeignKey("CompletedByUserId");

                    b.HasOne("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersion", "SupplierApplicationFormVersion")
                        .WithMany("Snapshots")
                        .HasForeignKey("SupplierApplicationFormVersionId");

                    b.HasOne("Theia.Domain.Entities.Reporting.TheiaAnalysisJob", "TheiaAnalysisJob")
                        .WithOne()
                        .HasForeignKey("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersionSnapshot", "TheiaAnalysisJobId");

                    b.Navigation("CompletedByUser");

                    b.Navigation("SupplierApplicationFormVersion");

                    b.Navigation("TheiaAnalysisJob");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Product", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "DeletedBy")
                        .WithMany()
                        .HasForeignKey("DeletedById");

                    b.HasOne("Theia.Domain.Entities.Suppliers.Supplier", "Supplier")
                        .WithMany("Products")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeletedBy");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionApplicationFormVersion", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersion", "SupplierApplicationFormVersion")
                        .WithMany("SupplierSubmissionRequestFormVersions")
                        .HasForeignKey("SupplierApplicationFormVersionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequest", "SupplierSubmission")
                        .WithMany("ApplicationFormVersions")
                        .HasForeignKey("SupplierSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SupplierApplicationFormVersion");

                    b.Navigation("SupplierSubmission");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionApplicationFormVersionSnapshot", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersionSnapshot", "SupplierApplicationFormVersionSnapshot")
                        .WithMany("SupplierSubmissions")
                        .HasForeignKey("SupplierApplicationFormVersionSnapshotId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequest", "SupplierSubmission")
                        .WithMany("Snapshots")
                        .HasForeignKey("SupplierSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SupplierApplicationFormVersionSnapshot");

                    b.Navigation("SupplierSubmission");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequest", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Identity.ApplicationUser", "CreatedByUser")
                        .WithMany("CreatedSupplierSubmissionRequests")
                        .HasForeignKey("CreatedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "Organisation")
                        .WithMany("SupplierSubmissions")
                        .HasForeignKey("OrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.Supplier", "Supplier")
                        .WithMany("SubmissionRequests")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedByUser");

                    b.Navigation("Organisation");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequestFile", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Suppliers.SupplierFile", "AssociatedSupplierFile")
                        .WithMany("AssociatedSubmissionFiles")
                        .HasForeignKey("AssociatedSupplierFileId");

                    b.HasOne("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequest", "SupplierSubmission")
                        .WithMany("Files")
                        .HasForeignKey("SupplierSubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssociatedSupplierFile");

                    b.Navigation("SupplierSubmission");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.SupplierFile", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Suppliers.Supplier", "Supplier")
                        .WithMany("Files")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.SupplierRequest", b =>
                {
                    b.HasOne("Theia.Domain.Entities.Organisations.Organisation", "CreatedByOrganisation")
                        .WithMany("SupplierRequests")
                        .HasForeignKey("CreatedByOrganisationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Suppliers.Services.SupplierServiceType", "SupplierType")
                        .WithMany()
                        .HasForeignKey("SupplierTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedByOrganisation");

                    b.Navigation("SupplierType");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Wholesale.WholesaleSubmission", b =>
                {
                    b.HasOne("Theia.Domain.Entities.BrokingHouses.BrokingHouse", "BrokingHouse")
                        .WithMany()
                        .HasForeignKey("BrokingHouseId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", "IndicationRequest")
                        .WithMany("WholesaleSubmissions")
                        .HasForeignKey("IndicationRequestId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("BrokingHouse");

                    b.Navigation("IndicationRequest");
                });

            modelBuilder.Entity("Theia.Domain.Entities.ApplicationForms.ApplicationFormVersion", b =>
                {
                    b.Navigation("Suppliers");
                });

            modelBuilder.Entity("Theia.Domain.Entities.ApplicationForms.ApplicationForms", b =>
                {
                    b.Navigation("Versions");
                });

            modelBuilder.Entity("Theia.Domain.Entities.BrokingHouses.BrokingHouse", b =>
                {
                    b.Navigation("OrgAssociations");

                    b.Navigation("SubmittedBrokerSubmissions");
                });

            modelBuilder.Entity("Theia.Domain.Entities.ControlFrameworks.ControlFramework", b =>
                {
                    b.Navigation("AnalysedControlFrameworks");

                    b.Navigation("ControlFrameworkCategories");
                });

            modelBuilder.Entity("Theia.Domain.Entities.ControlFrameworks.ControlFrameworkCategory", b =>
                {
                    b.Navigation("ControlFrameworkCategoryClauses");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationPermission", b =>
                {
                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationRole", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Identity.ApplicationUser", b =>
                {
                    b.Navigation("BrokerOrganisationRequests");

                    b.Navigation("BrokersBrokerSubmissions");

                    b.Navigation("Claims");

                    b.Navigation("CompletedByUser");

                    b.Navigation("CreatedSupplierSubmissionRequests");

                    b.Navigation("Indications");

                    b.Navigation("OrganisationRequests");

                    b.Navigation("QuotaShareFollowers");

                    b.Navigation("RequestedSubmissions");

                    b.Navigation("Roles");

                    b.Navigation("SubmissionQuestionAnswers");

                    b.Navigation("SubmissionQuestions");

                    b.Navigation("UnderwritersBrokerSubmissions");

                    b.Navigation("UserAccessibleTenants");

                    b.Navigation("UserAttachments");

                    b.Navigation("UserTenantControls");

                    b.Navigation("ViewedSubmissions");

                    b.Navigation("VoidedSubmission");

                    b.Navigation("WholesaleSubmissions");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.HeadOfCover", b =>
                {
                    b.Navigation("Options");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Indication", b =>
                {
                    b.Navigation("AnalysedControlFrameworks");

                    b.Navigation("Endorsements");

                    b.Navigation("IndicationOptions");

                    b.Navigation("Subjectives");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Layer", b =>
                {
                    b.Navigation("IndicationRequests");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.Option", b =>
                {
                    b.Navigation("HeadsOfCovers");

                    b.Navigation("QuotaShare");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.PolicyForm", b =>
                {
                    b.Navigation("HeadsOfCover");

                    b.Navigation("Indications");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.QuotaShare", b =>
                {
                    b.Navigation("Followers");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Indications.QuotaShareFollower", b =>
                {
                    b.Navigation("Underwriters");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Insurer", b =>
                {
                    b.Navigation("Indications");

                    b.Navigation("QuotaShares");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Notifications.Notification", b =>
                {
                    b.Navigation("NotificationRecipients");
                });

            modelBuilder.Entity("Theia.Domain.Entities.OrgBrokingAssociation", b =>
                {
                    b.Navigation("Brokers");
                });

            modelBuilder.Entity("Theia.Domain.Entities.OrganisationRequests.OrganisationRequest", b =>
                {
                    b.Navigation("Brokers");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Organisation", b =>
                {
                    b.Navigation("AnnualFinances");

                    b.Navigation("AssociatedSupplierProducts");

                    b.Navigation("BrokingAssociations");

                    b.Navigation("Contracts");

                    b.Navigation("CountryRevenues");

                    b.Navigation("DataCentreLocations");

                    b.Navigation("IndustryRevenues");

                    b.Navigation("OrganisationSuggestedProducts");

                    b.Navigation("Submissions");

                    b.Navigation("SupplierRequests");

                    b.Navigation("SupplierSubmissions");

                    b.Navigation("Suppliers");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Shared.Country", b =>
                {
                    b.Navigation("Organisations");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Shared.Industry", b =>
                {
                    b.Navigation("Organisations");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Profile.Shared.Region", b =>
                {
                    b.Navigation("Countries");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions.BrokerSubmission", b =>
                {
                    b.Navigation("AnalysedControlFrameworks");

                    b.Navigation("Brokers");

                    b.Navigation("Files");

                    b.Navigation("Indications");

                    b.Navigation("Questions");

                    b.Navigation("TheiaAnalysisJobs");

                    b.Navigation("Underwriters");

                    b.Navigation("Views");

                    b.Navigation("WholesaleSubmissions");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Submission", b =>
                {
                    b.Navigation("AnalysisJobs");

                    b.Navigation("ApplicationForms");

                    b.Navigation("Files");

                    b.Navigation("Layers");

                    b.Navigation("ProductSnapshots");

                    b.Navigation("Suppliers");

                    b.Navigation("ViewedBy");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionFile", b =>
                {
                    b.Navigation("BrokerSubmissions");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionQuestion", b =>
                {
                    b.Navigation("Answers");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.SubmissionSupplier", b =>
                {
                    b.Navigation("ApplicationFormsVersionSnapshots");

                    b.Navigation("AssociatedProducts");

                    b.Navigation("Files");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Suppliers.OrganisationSupplierAssociation", b =>
                {
                    b.Navigation("OrganisationSupplierAssociationSupplierServiceTypes");

                    b.Navigation("OrganisationSupplierAssociationSupplierServices");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.AnalysedControlFramework", b =>
                {
                    b.Navigation("AnalysedControlFrameworkCategories");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.AnalysedControlFrameworkCategory", b =>
                {
                    b.Navigation("AnalysedControlFrameworkCategoryClauses");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Reporting.TheiaAnalysisJob", b =>
                {
                    b.Navigation("AnalysedControlFrameworks");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersion", b =>
                {
                    b.Navigation("Snapshots");

                    b.Navigation("Submissions");

                    b.Navigation("SupplierSubmissionRequestFormVersions");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersionSnapshot", b =>
                {
                    b.Navigation("AnalysedControlFrameworks");

                    b.Navigation("Submissions");

                    b.Navigation("SupplierSubmissions");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Product", b =>
                {
                    b.Navigation("AssociatedProducts");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Services.SupplierService", b =>
                {
                    b.Navigation("OrganisationSupplierAssociationSupplierServices");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Services.SupplierServiceType", b =>
                {
                    b.Navigation("ServiceTypesForAssociatedSuppliers");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequest", b =>
                {
                    b.Navigation("ApplicationFormVersions");

                    b.Navigation("Files");

                    b.Navigation("Snapshots");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Submissions.SupplierSubmissionRequestFile", b =>
                {
                    b.Navigation("SubmissionSupplierFiles");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.Supplier", b =>
                {
                    b.Navigation("ApplicationFormVersions");

                    b.Navigation("Files");

                    b.Navigation("OrganisationSuggestedProducts");

                    b.Navigation("Organisations");

                    b.Navigation("Products");

                    b.Navigation("SubmissionRequests");

                    b.Navigation("Submissions");

                    b.Navigation("SupplierProducts");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Suppliers.SupplierFile", b =>
                {
                    b.Navigation("AssociatedSubmissionFiles");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Tenant", b =>
                {
                    b.Navigation("BrokingHouse");

                    b.Navigation("Insurer");

                    b.Navigation("Organisation");

                    b.Navigation("RequestedSubmissions");

                    b.Navigation("UserTenantControls");
                });

            modelBuilder.Entity("Theia.Domain.Entities.Organisations.Submissions.Wholesale.WholesaleSubmission", b =>
                {
                    b.Navigation("Brokers");
                });
#pragma warning restore 612, 618
        }
    }
}
