namespace Theia.Application.Common.Helpers.Validators;

public class MultiTenantRoleValidator : RoleValidator<ApplicationRole>
{
    private readonly ITenantResolverService _tenantResolverService;

    public MultiTenantRoleValidator(ITenantResolverService tenantResolverService)
    {
        _tenantResolverService = tenantResolverService;
    }

    public override async Task<IdentityResult> ValidateAsync(RoleManager<ApplicationRole> manager, ApplicationRole role)
    {
        bool isAddOperation = await manager.FindByIdAsync(role.Id) == null;

        bool combinationExists = await CheckCombinationExists(manager, role, isAddOperation);

        return combinationExists
            ? IdentityResult.Failed(new IdentityError
            {
                Code = "DuplicateRoleName",
                Description = _tenantResolverService.TenantMode == TenantMode.MultiTenant &&
                              !_tenantResolverService.IsHost
                    ? Resource.The_specified_role_is_already_registered_in_the_given_tenant
                    : Resource.The_specified_role_is_already_registered
            })
            : IdentityResult.Success;
    }

    private static async Task<bool> CheckCombinationExists(RoleManager<ApplicationRole> roleManager,
        ApplicationRole role,
        bool isAddOperation)
    {
        bool combinationExists;
        if (isAddOperation)
        {
            combinationExists = await roleManager.Roles.AnyAsync(
                r => r.Name == role.Name);
        }
        else
        {
            combinationExists = await roleManager.Roles.Where(r => r.Id != role.Id)
                .AnyAsync(r => r.Name == role.Name);
        }

        return combinationExists;
    }
}